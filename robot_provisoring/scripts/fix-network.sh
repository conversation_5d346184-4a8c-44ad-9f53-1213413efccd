#!/bin/bash
# fix-network.sh - Naprawa konfiguracji sieci po reboocie

set -e

echo "=== ARGUS Robot - Naprawa sieci ==="
echo "Data: $(date)"
echo ""

# Sprawdź status NetworkManager
echo "1. Sprawdzanie NetworkManager..."
if ! systemctl is-active --quiet NetworkManager; then
    echo "Uruchamianie NetworkManager..."
    systemctl start NetworkManager
    sleep 3
fi

# Sprawdź czy połączenie ARGUS-AP istnieje
echo "2. Sprawdzanie połączenia ARGUS-AP..."
if ! nmcli con show "ARGUS-AP" >/dev/null 2>&1; then
    echo "Połączenie ARGUS-AP nie istnieje - tworzenie..."
    
    # Usuń stare połączenia WiFi na wlan0
    nmcli con show | grep wlan0 | awk '{print $1}' | while read con; do
        nmcli con delete "$con" 2>/dev/null || true
    done
    
    # Stwórz nowe połączenie Access Point
    nmcli con add type wifi ifname wlan0 con-name "ARGUS-AP" autoconnect yes ssid "ARGUS"
    nmcli con modify "ARGUS-AP" 802-11-wireless.mode ap
    nmcli con modify "ARGUS-AP" 802-11-wireless.band bg
    nmcli con modify "ARGUS-AP" 802-11-wireless.channel 7
    nmcli con modify "ARGUS-AP" ipv4.method manual
    nmcli con modify "ARGUS-AP" ipv4.addresses ***********/24
    nmcli con modify "ARGUS-AP" ipv4.gateway ***********
    nmcli con modify "ARGUS-AP" wifi-sec.key-mgmt wpa-psk
    nmcli con modify "ARGUS-AP" wifi-sec.psk "argusik!23"
    
    echo "✓ Połączenie ARGUS-AP utworzone"
else
    echo "✓ Połączenie ARGUS-AP istnieje"
    
    # Sprawdź i popraw konfigurację IPv4
    echo "3. Sprawdzanie konfiguracji IPv4..."
    CURRENT_METHOD=$(nmcli con show "ARGUS-AP" | grep ipv4.method | awk '{print $2}')
    if [ "$CURRENT_METHOD" != "manual" ]; then
        echo "Poprawianie metody IPv4 z '$CURRENT_METHOD' na 'manual'..."
        nmcli con modify "ARGUS-AP" ipv4.method manual
        nmcli con modify "ARGUS-AP" ipv4.addresses ***********/24
        nmcli con modify "ARGUS-AP" ipv4.gateway ***********
    fi
fi

# Wyłącz i włącz WiFi
echo "4. Resetowanie interfejsu WiFi..."
nmcli radio wifi off
sleep 2
nmcli radio wifi on
sleep 3

# Aktywuj połączenie
echo "5. Aktywacja połączenia ARGUS-AP..."
nmcli con down "ARGUS-AP" 2>/dev/null || true
sleep 2
nmcli con up "ARGUS-AP"

# Sprawdź rezultat
echo "6. Sprawdzanie rezultatu..."
sleep 5

WLAN0_IP=$(ip addr show wlan0 | grep "inet " | awk '{print $2}' | head -n1)
if [ -n "$WLAN0_IP" ]; then
    echo "✓ Interfejs wlan0 ma IP: $WLAN0_IP"
else
    echo "✗ Interfejs wlan0 nadal nie ma IP"
    
    # Próba ręcznego przypisania IP
    echo "7. Próba ręcznego przypisania IP..."
    ip addr add ***********/24 dev wlan0 2>/dev/null || true
    
    WLAN0_IP=$(ip addr show wlan0 | grep "inet " | awk '{print $2}' | head -n1)
    if [ -n "$WLAN0_IP" ]; then
        echo "✓ IP przypisane ręcznie: $WLAN0_IP"
    else
        echo "✗ Nie udało się przypisać IP"
    fi
fi

# Sprawdź czy Access Point jest widoczny
echo "8. Sprawdzanie widoczności Access Point..."
if nmcli con show --active | grep -q "ARGUS-AP"; then
    echo "✓ Access Point ARGUS-AP jest aktywny"
else
    echo "✗ Access Point ARGUS-AP nie jest aktywny"
fi

# Pokaż status końcowy
echo ""
echo "=== Status końcowy ==="
echo "NetworkManager:"
nmcli dev status | grep wlan0 || echo "wlan0 nie znaleziony"
echo ""
echo "Aktywne połączenia:"
nmcli con show --active
echo ""
echo "IP wlan0:"
ip addr show wlan0 | grep "inet " || echo "Brak IP"

echo ""
echo "=== Naprawa zakończona ==="
echo "Jeśli problem nadal występuje, sprawdź logi:"
echo "  journalctl -u NetworkManager -f"
echo "  dmesg | grep wlan"
