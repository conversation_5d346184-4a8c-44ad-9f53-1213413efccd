#!/bin/bash
# 06-services.sh - Konfiguracja uslug systemowych i aplikacji ARGUS

set -e

echo "=== Konfiguracja uslug systemowych ==="

# Tworzenie katalogow dla logow
echo "Tworzenie katalogow systemowych..."
mkdir -p /var/log/argusik

# Okreslenie sciezki do katalogu provisioning
PROVISION_DIR="$(dirname "$(dirname "$(realpath "$0")")")"

# Kopiowanie plikow uslug streamingu (narzedzia systemowe)
if [ -d "$PROVISION_DIR/files/service" ]; then
    echo "Kopiowanie plikow streamingu..."
    mkdir -p /opt/argusik/bin
    cp -r "$PROVISION_DIR/files/service"/* /opt/argusik/bin/
    chmod +x /opt/argusik/bin/*.sh 2>/dev/null || true
    chmod +x /opt/argusik/bin/rtsp-simple-server 2>/dev/null || true
fi

# Kopiowanie i instalacja uslugi systemowej
echo "Instalacja uslugi systemowej..."
if [ -f "$PROVISION_DIR/services/argus_core.service" ]; then
    cp "$PROVISION_DIR/services/argus_core.service" /etc/systemd/system/
    systemctl daemon-reload
    systemctl enable argus_core.service
fi

# Tworzenie struktury katalogow ROS2 (jesli nie istnieja)
echo "Sprawdzanie struktury katalogow ROS2..."
mkdir -p /root/src_ros2
mkdir -p /root/ros2_jazzy/src

# Pobieranie kodu zrodlowego robot_core z GitLab
echo "Pobieranie kodu zrodlowego robot_core z GitLab..."
cd /root/src_ros2

# Sprawdzenie czy git jest dostepny
if ! command -v git &> /dev/null; then
    echo "BlaD: Git nie jest zainstalowany!"
    exit 1
fi

# Wczytanie konfiguracji GitLab
GITLAB_CONFIG="$PROVISION_DIR/config/gitlab.conf"
if [ -f "$GITLAB_CONFIG" ]; then
    echo "Wczytywanie konfiguracji GitLab z $GITLAB_CONFIG"
    source "$GITLAB_CONFIG"
else
    echo "UWAGA: Nie znaleziono pliku konfiguracyjnego GitLab, uzywam domyslnych wartosci"
    ROBOT_CORE_REPO_URL="https://gitlab.devforyou.pl/avotech/robot_core.git"
    ROBOT_CORE_BRANCH="main"
    GIT_CLONE_OPTIONS="--single-branch"
fi

echo "Klonowanie repozytorium: $ROBOT_CORE_REPO_URL (branch: $ROBOT_CORE_BRANCH)"

# Konfiguracja git dla self-hosted GitLab
if [ "${GITLAB_SELF_HOSTED:-false}" = "true" ]; then
    echo "Konfiguracja git dla self-hosted GitLab..."

    # SSL verification
    if [ "${GIT_SSL_VERIFY:-true}" = "false" ]; then
        echo "Wylaczanie weryfikacji SSL dla self-hosted GitLab"
        git config --global http.sslVerify false
    fi

    # HTTP timeouts
    git config --global http.lowSpeedLimit "${GIT_HTTP_LOW_SPEED_LIMIT:-1000}"
    git config --global http.lowSpeedTime "${GIT_HTTP_LOW_SPEED_TIME:-60}"
    git config --global http.postBuffer 524288000
fi

# Klonowanie z timeout
if timeout "${GIT_TIMEOUT:-300}" git clone $GIT_CLONE_OPTIONS --branch "$ROBOT_CORE_BRANCH" "$ROBOT_CORE_REPO_URL" robot_core; then
    echo "Pomyslnie pobrano robot_core z GitLab"

    # Linkowanie pakietow do workspace ROS2
    echo "Linkowanie pakietow robot_core do workspace ROS2..."
    if [ -d "/root/src_ros2/robot_core" ]; then
        # Usun stare linki jesli istnieja
        find /root/ros2_jazzy/src/ -type l -delete 2>/dev/null || true

        # Linkuj kazdy pakiet ROS2 osobno
        for package_dir in /root/src_ros2/robot_core/*/; do
            if [ -f "$package_dir/package.xml" ]; then
                package_name=$(basename "$package_dir")
                echo "Linkowanie pakietu: $package_name"
                ln -sf "$package_dir" "/root/ros2_jazzy/src/$package_name"
            fi
        done

        # Jesli robot_core ma glowny package.xml, linkuj tez glowny katalog
        if [ -f "/root/src_ros2/robot_core/package.xml" ]; then
            echo "Linkowanie glownego pakietu robot_core"
            ln -sf "/root/src_ros2/robot_core" "/root/ros2_jazzy/src/robot_core"
        fi

        echo "Pakiety robot_core zlinkowane do workspace"
    else
        echo "BlaD: Nie znaleziono pobranego katalogu robot_core"
        exit 1
    fi
else
    echo "BlaD: Nie udalo sie pobrac repozytorium robot_core"
    echo "Sprawdz:"
    echo "1. URL repozytorium: $ROBOT_CORE_REPO_URL"
    echo "2. Polaczenie z self-hosted GitLab"
    echo "3. Dane uwierzytelniajace (jesli repo prywatne)"
    echo "4. Certyfikaty SSL (ustaw GIT_SSL_VERIFY=false jesli potrzeba)"

    # Test polaczenia z GitLab
    GITLAB_HOST=$(echo "$ROBOT_CORE_REPO_URL" | sed -n 's|https\?://\([^/]*\).*|\1|p')
    if [ -n "$GITLAB_HOST" ]; then
        echo "Testowanie polaczenia z $GITLAB_HOST..."
        if ping -c 3 "$GITLAB_HOST" &>/dev/null; then
            echo "✓ Polaczenie sieciowe z $GITLAB_HOST dziala"
        else
            echo "✗ Brak polaczenia sieciowego z $GITLAB_HOST"
        fi

        if curl -s --connect-timeout 10 "https://$GITLAB_HOST" &>/dev/null; then
            echo "✓ HTTPS polaczenie z $GITLAB_HOST dziala"
        else
            echo "✗ Problem z HTTPS polaczeniem do $GITLAB_HOST"
        fi
    fi

    exit 1
fi

# Budowanie workspace ROS2 z nowymi pakietami
echo "Budowanie workspace ROS2 z pakietami robot_core..."
cd /root/ros2_jazzy

# Source ROS2 i budowanie tylko nowych pakietow
source /root/ros2_jazzy/install/setup.bash
rosdep install --from-paths src --ignore-src -r -y || true
colcon build --symlink-install --packages-select $(find src -name package.xml -exec dirname {} \; | xargs -I {} basename {})

# Konfiguracja logowania
echo "Konfiguracja logowania..."
cat > /etc/logrotate.d/argusik << 'EOF'
/var/log/argusik/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 root root
}
EOF

# Tworzenie skryptu diagnostycznego
echo "Tworzenie skryptu diagnostycznego..."
cat > /opt/argusik/bin/argus_diagnostics.sh << 'EOF'
#!/bin/bash
# ARGUS Robot Diagnostics Script

echo "=== ARGUS Robot Diagnostics ==="
echo "Date: $(date)"
echo "Hostname: $(hostname)"
echo "IP Address: $(hostname -I)"
echo ""

echo "=== System Info ==="
echo "OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
echo "Kernel: $(uname -r)"
echo "Uptime: $(uptime -p)"
echo "Load: $(uptime | awk -F'load average:' '{print $2}')"
echo ""

echo "=== ROS2 Status ==="
source /root/ros2_jazzy/install/setup.bash 2>/dev/null || echo "ROS2 not sourced"
echo "ROS_DISTRO: $ROS_DISTRO"
echo "ROS_DOMAIN_ID: $ROS_DOMAIN_ID"
echo ""

echo "=== Services Status ==="
systemctl is-active argus_core.service || echo "argus_core: inactive"
systemctl is-active hostapd.service || echo "hostapd: inactive"
systemctl is-active dnsmasq.service || echo "dnsmasq: inactive"
echo ""

echo "=== Network Status ==="
ip addr show wlan0 | grep inet || echo "wlan0: no IP"
ip addr show eth0 | grep inet || echo "eth0: no IP"
echo ""

echo "=== Hardware Status ==="
vcgencmd measure_temp
vcgencmd get_throttled
echo ""

echo "=== Disk Usage ==="
df -h / /boot
echo ""

echo "=== Recent Logs ==="
tail -n 10 /var/log/argusik/*.log 2>/dev/null || echo "No ARGUS logs found"
EOF

chmod +x /opt/argusik/bin/argus_diagnostics.sh

# Dodanie aliasu dla diagnostyki
echo "alias argus-diag='/opt/argusik/bin/argus_diagnostics.sh'" >> /root/.bashrc
echo "alias argus-diag='sudo /opt/argusik/bin/argus_diagnostics.sh'" >> /home/<USER>/.bashrc 2>/dev/null || true

echo "=== Konfiguracja uslug zakonczona ==="