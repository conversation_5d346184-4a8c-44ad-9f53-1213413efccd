[Unit]
Description=ARGUS Robot Core Service
After=network.target multi-user.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/root/ros2_jazzy
Environment="ROS_DOMAIN_ID=42"
Environment="ROS_LOCALHOST_ONLY=0"
ExecStart=/opt/argusik/bin/start_ros2.sh
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=argus_core

# Logging
StandardOutput=append:/var/log/argusik/argus_core.log
StandardError=append:/var/log/argusik/argus_core_error.log

[Install]
WantedBy=multi-user.target