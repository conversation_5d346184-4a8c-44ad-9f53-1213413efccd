# 🚀 ARGUS Robot - <PERSON><PERSON>b<PERSON> Start

## Dla nowych uzytkownikow

### 1. Przygotowanie Raspberry Pi
- Zainstaluj **Raspbian Bookworm** na karcie microSD
- Wlacz SSH w raspi-config
- Podlacz Raspberry Pi do sieci LAN (Ethernet)

### 2. Pobranie i instalacja
```bash
# Zaloguj sie na Raspberry Pi
ssh argus@<IP_RASPBERRY_PI>
sudo su

# Pobierz projekt
git clone <repository-url>
cd robot_provisioning

# skonfiguruj GitLab
nano config/gitlab.conf

# Uruchom instalacje
sudo ./install.sh
```

### 3. Czekaj na zakonczenie
- System zostanie automatycznie zrestartowany

### 4. <PERSON><PERSON>z sie z robotem
Po restarcie robot bedzie dostepny jako Access Point:
- **SSID:** `ARGUS`
- **Haslo:** `argusik!23`
- **IP:** `***********`

```bash
# <PERSON><PERSON><PERSON> sie przez SSH
ssh argusik@***********
```


### Diagnostyka
```bash
sudo /opt/argusik/bin/argus_diagnostics.sh
```


### Sprawdz status
```bash
# Status glownej uslugi
sudo systemctl status argus_core

# Lista wezlow ROS2
ros2 node list
```

## Rozwiazywanie problemow

### Robot nie uruchamia sie
```bash
sudo journalctl -u argus_core -f
```

### Brak Access Point
```bash
sudo systemctl status hostapd
sudo systemctl restart hostapd
```

### Problemy z ROS2
```bash
source /opt/ros/jazzy/setup.bash
ros2 doctor
```

## Przydatne aliasy

Po instalacji dostepne sa aliasy:
```bash
argus-status    # Status uslugi
argus-restart   # Restart uslugi  
argus-logs      # Podglad logow
argus-diag      # Diagnostyka
```

## Wsparcie

- 📖 Pelna dokumentacja: `README.md`
- 🔧 Przydatne komendy: `COMMANDS.md`
- 🐛 Problemy: Sprawdz logi w `/var/log/argusik/`

---

**⚠️ PAMIETAJ:** Zmien domyslne hasla po instalacji!
